import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:http/http.dart' as http;
import 'package:hekimmind/domain/repositories/analysis_repository.dart';
import 'package:hekimmind/data/models/analysis_result_model.dart';
import 'package:hekimmind/data/models/patient_data_model.dart';
import 'package:hekimmind/services/prompt_service.dart';
import 'package:hekimmind/core/config/app_config.dart';

class AnalysisRepositoryImpl implements AnalysisRepository {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;
  final String _openAIApiKey;

  AnalysisRepositoryImpl({
    FirebaseFirestore? firestore,
    FirebaseStorage? storage,
    required String openAIApiKey,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _storage = storage ?? FirebaseStorage.instance,
        _openAIApiKey = openAIApiKey;

  @override
  Future<String> uploadImage(File image, String userId) async {
    print('📤 Firebase Storage upload başlatılıyor...');
    print('📤 Storage bucket: ${_storage.bucket}');
    print('📤 User ID: $userId');
    print('📤 Image path: ${image.path}');
    print('📤 Image exists: ${await image.exists()}');
    print('📤 Image size: ${await image.length()} bytes');

    try {
      final fileName = '${DateTime.now().millisecondsSinceEpoch}_${userId}.jpg';
      final ref = _storage.ref().child('analysis_images/$userId/$fileName');
      print('📤 Firebase Storage ref oluşturuldu: analysis_images/$userId/$fileName');

      // Metadata ekle
      final metadata = SettableMetadata(
        contentType: 'image/jpeg',
        customMetadata: {
          'userId': userId,
          'uploadTime': DateTime.now().toIso8601String(),
        },
      );

      print('📤 Upload başlatılıyor...');
      final uploadTask = ref.putFile(image, metadata);

      // Upload progress'i takip et
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        final progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
        print('📤 Upload progress: ${progress.toStringAsFixed(1)}%');
      });

      final snapshot = await uploadTask;
      print('📤 Upload tamamlandı! State: ${snapshot.state}');

      final downloadUrl = await snapshot.ref.getDownloadURL();
      print('📤 Firebase Storage upload başarılı: $downloadUrl');
      return downloadUrl;
    } catch (e) {
      print('❌ Firebase Storage hatası: $e');
      print('❌ Hata türü: ${e.runtimeType}');
      if (e is FirebaseException) {
        print('❌ Firebase error code: ${e.code}');
        print('❌ Firebase error message: ${e.message}');
      }
      rethrow; // Mock URL döndürme, gerçek hatayı fırlat
    }
  }

  @override
  Future<AnalysisResultModel> analyzeImage(
    String imageUrl,
    String userId,
    String analysisType,
    PatientDataModel patientData,
  ) async {
    try {
      print('🔍 Analiz başlatılıyor...');
      print('📷 Image URL: $imageUrl');
      print('👤 User ID: $userId');
      print('🔬 Analysis Type: $analysisType');

      // Prompt oluştur
      final prompt = PromptService.generatePrompt(analysisType, patientData);
      print('📝 Prompt oluşturuldu: ${prompt.substring(0, 100)}...');

      // OpenAI API'ye istek gönder
      final requestBody = {
        'model': AppConfig.openAIModel,
        'messages': [
          {
            'role': 'user',
            'content': [
              {
                'type': 'text',
                'text': prompt,
              },
              {
                'type': 'image_url',
                'image_url': {
                  'url': imageUrl,
                },
              },
            ],
          },
        ],
        'max_tokens': AppConfig.maxTokens,
        'temperature': 0.3,
      };

      print('🚀 OpenAI API\'ye istek gönderiliyor...');
      final response = await http.post(
        Uri.parse('${AppConfig.apiBaseUrl}/chat/completions'),
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          'Authorization': 'Bearer ${AppConfig.openAIApiKey}',
          'Accept': 'application/json',
          'Accept-Charset': 'utf-8',
        },
        body: utf8.encode(jsonEncode(requestBody)),
      );

      print('📡 API Response Status: ${response.statusCode}');

      // UTF-8 decode ile Türkçe karakter desteği
      final responseBody = utf8.decode(response.bodyBytes);
      print('📡 API Response Body: ${responseBody.substring(0, 200)}...');

      if (response.statusCode != 200) {
        throw Exception('OpenAI API hatası: ${response.statusCode} - $responseBody');
      }

      final responseData = jsonDecode(responseBody);
      final analysisResult = responseData['choices'][0]['message']['content'];
      print('✅ Analiz sonucu alındı: ${analysisResult.substring(0, 100)}...');

      final result = AnalysisResultModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        imageUrl: imageUrl,
        analysisType: analysisType,
        result: analysisResult,
        timestamp: DateTime.now(),
      );

      // Sonucu hasta bilgileri ile birlikte Firestore'a kaydet
      print('💾 Firestore\'a kaydediliyor...');
      final analysisData = result.toJson();
      analysisData['patientData'] = patientData.toJson();
      analysisData['promptUsed'] = prompt;

      await _firestore.collection('medical_analyses').add(analysisData);
      print('✅ Firestore\'a kaydedildi!');

      return result;
    } catch (e) {
      print('❌ Hata oluştu: $e');
      rethrow;
    }
  }



  @override
  Future<List<AnalysisResultModel>> getAnalysisHistory(String userId) async {
    // UserId kontrolü
    if (userId.isEmpty || userId == 'anonymous') {
      return []; // Boş liste döndür
    }

    final snapshot = await _firestore
        .collection('medical_analyses')
        .where('userId', isEqualTo: userId)
        .get();

    final results = snapshot.docs
        .map((doc) => AnalysisResultModel.fromJson(doc.data()))
        .toList();

    // Timestamp'e göre sırala (en yeni önce)
    results.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return results;
  }
}
