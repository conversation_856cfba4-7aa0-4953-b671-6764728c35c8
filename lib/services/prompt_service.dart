import 'package:hekimmind/data/models/patient_data_model.dart';

class PromptService {
  static String generatePrompt(String analysisType, PatientDataModel patientData) {
    final basePrompt = _getBasePromptForAnalysisType(analysisType);
    final patientInfo = _formatPatientInfo(patientData);
    final ageGroupGuidelines = _getAgeGroupGuidelines(patientData);
    final genderSpecificGuidelines = _getGenderSpecificGuidelines(patientData, analysisType);
    final instructions = _getInstructionsForAnalysisType(analysisType);

    return '''
$basePrompt

HASTA BİLGİLERİ:
$patientInfo

YAŞ GRUBU DEĞERLENDİRME KRİTERLERİ:
$ageGroupGuidelines

CİNSİYET-SPESİFİK DEĞERLENDİRME KRİTERLERİ:
$genderSpecificGuidelines

GÖREV:
$instructions

YANIT FORMATI:
Lütfen yanıtınızı aşağıdaki yapıda verin:

**GENEL DEĞERLENDİRME:**
[<PERSON><PERSON><PERSON><PERSON><PERSON>ünün genel durumu hakkında kısa a<PERSON>ıkla<PERSON>]

**BULGULAR:**
[Tespit edilen önemli bulgular]

**ÖNERİLER:**
[Önerilen takip adımları]

**UYARI:**
Bu analiz sadece yardımcı bir görüş niteliğindedir. Kesin tanı için mutlaka uzman hekime başvurunuz.
''';
  }

  static String _getBasePromptForAnalysisType(String analysisType) {
    switch (analysisType.toUpperCase()) {
      case 'MR':
        return '''Sen deneyimli bir radyolog uzmansın. Bu MR (Manyetik Rezonans) görüntüsünü detaylı olarak analiz et. 
Anatomik yapıları, olası patolojileri ve normal/anormal bulguları değerlendir.''';
        
      case 'EKG':
        return '''Sen deneyimli bir kardiyolog uzmansın. Bu EKG (Elektrokardiyografi) görüntüsünü detaylı olarak analiz et.
Ritim, kalp hızı, dalga formları ve olası aritmileri değerlendir.''';
        
      case 'RÖNTGEN':
        return '''Sen deneyimli bir radyolog uzmansın. Bu röntgen görüntüsünü detaylı olarak analiz et.
Kemik yapıları, yumuşak dokular ve olası patolojileri değerlendir.''';
        
      case 'TOMOGRAFİ':
        return '''Sen deneyimli bir radyolog uzmansın. Bu tomografi (BT) görüntüsünü detaylı olarak analiz et.
Kesitsel anatomik yapıları, yoğunluk değerlerini ve olası patolojileri değerlendir.''';
        
      case 'DERİ':
        return '''Sen deneyimli bir dermatoloji uzmansın. Bu deri lezyonu görüntüsünü detaylı olarak analiz et.
Lezyonun karakteristiklerini, rengini, şeklini ve olası tanıları değerlendir.''';
        
      case 'ULTRASON':
        return '''Sen deneyimli bir radyolog uzmansın. Bu ultrason görüntüsünü detaylı olarak analiz et.
Ekojenite, organ yapıları ve olası patolojileri değerlendir.''';
        
      default:
        return '''Sen deneyimli bir tıp uzmanısın. Bu tıbbi görüntüyü detaylı olarak analiz et.''';
    }
  }

  static String _formatPatientInfo(PatientDataModel patientData) {
    final buffer = StringBuffer();
    
    if (patientData.age != null && patientData.age!.isNotEmpty) {
      buffer.writeln('• Yaş: ${patientData.age}');
    }
    buffer.writeln('• Cinsiyet: ${patientData.gender}');
    
    if (patientData.height != null && patientData.height!.isNotEmpty) {
      buffer.writeln('• Boy: ${patientData.height} cm');
    }
    
    if (patientData.weight != null && patientData.weight!.isNotEmpty) {
      buffer.writeln('• Kilo: ${patientData.weight} kg');
    }
    
    if (patientData.symptoms != null && patientData.symptoms!.isNotEmpty) {
      buffer.writeln('• Şikayetler: ${patientData.symptoms}');
    }
    
    if (patientData.symptomDuration != null && patientData.symptomDuration!.isNotEmpty) {
      buffer.writeln('• Şikayet Süresi: ${patientData.symptomDuration}');
    }
    
    if (patientData.medicalHistory != null && patientData.medicalHistory!.isNotEmpty) {
      buffer.writeln('• Tıbbi Geçmiş: ${patientData.medicalHistory}');
    }
    
    if (patientData.familyHistory != null && patientData.familyHistory!.isNotEmpty) {
      buffer.writeln('• Aile Öyküsü: ${patientData.familyHistory}');
    }
    
    if (patientData.medications != null && patientData.medications!.isNotEmpty) {
      buffer.writeln('• Kullandığı İlaçlar: ${patientData.medications}');
    }
    
    if (patientData.allergies != null && patientData.allergies!.isNotEmpty) {
      buffer.writeln('• Alerjiler: ${patientData.allergies}');
    }
    
    if (patientData.reasonForExam != null && patientData.reasonForExam!.isNotEmpty) {
      buffer.writeln('• Tetkik Nedeni: ${patientData.reasonForExam}');
    }
    
    // Yaşam tarzı bilgileri
    final lifestyle = <String>[];
    if (patientData.smoker) lifestyle.add('Sigara içiyor');
    if (patientData.alcohol) lifestyle.add('Alkol kullanıyor');
    if (patientData.physicallyActive) lifestyle.add('Fiziksel olarak aktif');
    
    if (lifestyle.isNotEmpty) {
      buffer.writeln('• Yaşam Tarzı: ${lifestyle.join(', ')}');
    }
    
    if (patientData.additionalNotes != null && patientData.additionalNotes!.isNotEmpty) {
      buffer.writeln('• Ek Notlar: ${patientData.additionalNotes}');
    }
    
    return buffer.toString();
  }

  static String _getInstructionsForAnalysisType(String analysisType) {
    switch (analysisType.toUpperCase()) {
      case 'MR':
        return '''Bu MR görüntüsünü hasta bilgileri ışığında analiz et. Özellikle:
- Anatomik yapıların normal/anormal durumu
- Sinyal intensitelerindeki değişiklikler
- Olası patolojik bulgular
- Yaş ve cinsiyete uygun değerlendirme''';
        
      case 'EKG':
        return '''Bu EKG görüntüsünü hasta bilgileri ışığında analiz et. Özellikle:
- Kalp ritmi ve hızı
- P, QRS, T dalgalarının değerlendirmesi
- Olası aritmiler veya iskemi bulguları
- Yaş ve cinsiyete uygun normal değerler''';
        
      case 'RÖNTGEN':
        return '''Bu röntgen görüntüsünü hasta bilgileri ışığında analiz et. Özellikle:
- Kemik yapılarının değerlendirmesi
- Yumuşak doku gölgeleri
- Olası kırık, enfeksiyon veya tümör bulguları
- Yaşa uygun değişiklikler''';
        
      case 'TOMOGRAFİ':
        return '''Bu tomografi görüntüsünü hasta bilgileri ışığında analiz et. Özellikle:
- Kesitsel anatomik yapılar
- Yoğunluk değerleri ve kontrastlanma
- Olası kitle, enfeksiyon veya vasküler patolojiler
- Yaş ve cinsiyete uygun bulgular''';
        
      case 'DERİ':
        return '''Bu deri lezyonu görüntüsünü hasta bilgileri ışığında analiz et. Özellikle:
- ABCDE kriterlerine göre değerlendirme
- Lezyonun renk, şekil ve sınır özellikleri
- Olası benign/malign ayırımı
- Yaş ve cinsiyete özgü deri değişiklikleri''';
        
      case 'ULTRASON':
        return '''Bu ultrason görüntüsünü hasta bilgileri ışığında analiz et. Özellikle:
- Organ ekojenitesi ve yapısal özellikler
- Olası kitle, kist veya diğer patolojiler
- Doppler bulguları (varsa)
- Yaş ve cinsiyete uygun normal varyasyonlar''';
        
      default:
        return '''Bu tıbbi görüntüyü hasta bilgileri ışığında detaylı olarak analiz et.''';
    }
  }
}
