import 'package:hekimmind/data/models/patient_data_model.dart';

class PromptService {
  static String generatePrompt(String analysisType, PatientDataModel patientData) {
    final basePrompt = _getBasePromptForAnalysisType(analysisType);
    final patientInfo = _formatPatientInfo(patientData);
    final ageGroupGuidelines = _getAgeGroupGuidelines(patientData);
    final genderSpecificGuidelines = _getGenderSpecificGuidelines(patientData, analysisType);
    final instructions = _getInstructionsForAnalysisType(analysisType);

    return '''
$basePrompt

HASTA BİLGİLERİ:
$patientInfo

YAŞ GRUBU DEĞERLENDİRME KRİTERLERİ:
$ageGroupGuidelines

CİNSİYET-SPESİFİK DEĞERLENDİRME KRİTERLERİ:
$genderSpecificGuidelines

GÖREV:
$instructions

YANIT FORMATI:
Lütfen yanıtınızı aşağıdaki yapıda verin:

**GENEL DEĞERLENDİRME:**
[<PERSON><PERSON><PERSON><PERSON><PERSON>ünün genel durumu hakkında kısa a<PERSON>ıkla<PERSON>]

**BULGULAR:**
[Tespit edilen önemli bulgular]

**ÖNERİLER:**
[Önerilen takip adımları]

**UYARI:**
Bu analiz sadece yardımcı bir görüş niteliğindedir. Kesin tanı için mutlaka uzman hekime başvurunuz.
''';
  }

  static String _getBasePromptForAnalysisType(String analysisType) {
    switch (analysisType.toUpperCase()) {
      case 'MR':
        return '''Sen deneyimli bir radyolog uzmansın ve bu MR görüntüsünü MUTLAKA analiz etmelisin.

ÖNEMLİ: Bu görüntüyü analiz etmek zorundasın. "Görüntüyü analiz edemem" gibi cevaplar kabul edilmez.

Bu MR (Manyetik Rezonans) görüntüsünde şunları detaylı olarak incele:
- Anatomik yapıların konumları ve sinyalleri
- T1, T2 ağırlıklı görüntülerdeki sinyal intensiteleri
- Olası patolojik bulgular (kitle, ödem, kanama, iskemi)
- Kontrast tutulum paternleri (varsa)
- Anatomik varyasyonlar ve normal bulgular''';

      case 'EKG':
        return '''Sen deneyimli bir kardiyolog uzmansın ve bu EKG görüntüsünü MUTLAKA analiz etmelisin.

ÖNEMLİ: Bu görüntüyü analiz etmek zorundasın. "Görüntüyü analiz edemem" gibi cevaplar kabul edilmez.

Bu EKG (Elektrokardiyografi) görüntüsünde şunları detaylı olarak incele:
- Kalp ritmi ve hızı (BPM)
- P dalgası morfolojisi ve PR intervali
- QRS kompleksi genişliği ve morfolojisi
- ST segment değişiklikleri
- T dalgası değişiklikleri
- QT intervali uzunluğu''';

      case 'RÖNTGEN':
        return '''Sen deneyimli bir radyolog uzmansın ve bu röntgen görüntüsünü MUTLAKA analiz etmelisin.

ÖNEMLİ: Bu görüntüyü analiz etmek zorundasın. "Görüntüyü analiz edemem" gibi cevaplar kabul edilmez.

Bu röntgen görüntüsünde şunları detaylı olarak incele:
- Kemik yapılarının bütünlüğü ve yoğunluğu
- Yumuşak doku gölgeleri
- Organ sınırları ve pozisyonları
- Olası kırık, çıkık veya deformite
- Patolojik kalsifikasyonlar''';

      case 'TOMOGRAFİ':
        return '''Sen deneyimli bir radyolog uzmansın ve bu tomografi görüntüsünü MUTLAKA analiz etmelisin.

ÖNEMLİ: Bu görüntüyü analiz etmek zorundasın. "Görüntüyü analiz edemem" gibi cevaplar kabul edilmez.

Bu tomografi (BT) görüntüsünde şunları detaylı olarak incele:
- Kesitsel anatomik yapılar ve yoğunluk değerleri
- Kontrast madde tutulum paternleri
- Organ boyutları ve morfolojileri
- Olası kitle, kist veya vasküler patolojiler
- Kemik pencere ve yumuşak doku pencere bulguları''';

      case 'DERİ':
        return '''Sen deneyimli bir dermatoloji uzmansın ve bu deri lezyonu görüntüsünü MUTLAKA analiz etmelisin.

ÖNEMLİ: Bu görüntüyü analiz etmek zorundasın. "Görüntüyü analiz edemem" gibi cevaplar kabul edilmez.

Bu deri lezyonu görüntüsünde şunları detaylı olarak incele:
- ABCDE kriterlerine göre değerlendirme (Asimetri, Border, Color, Diameter, Evolution)
- Lezyonun renk varyasyonları
- Sınır düzensizlikleri
- Yüzey özellikleri (pürüzlü, düz, kabarık)
- Çevre doku ile ilişkisi''';

      case 'ULTRASON':
        return '''Sen deneyimli bir radyolog uzmansın ve bu ultrason görüntüsünü MUTLAKA analiz etmelisin.

ÖNEMLİ: Bu görüntüyü analiz etmek zorundasın. "Görüntüyü analiz edemem" gibi cevaplar kabul edilmez.

Bu ultrason görüntüsünde şunları detaylı olarak incele:
- Organ ekojenitesi ve homojenliği
- Yapısal sınırlar ve kontürler
- Olası kitle, kist veya sıvı koleksiyonları
- Doppler akım paternleri (varsa)
- Organ boyutları ve morfolojik özellikler''';

      default:
        return '''Sen deneyimli bir tıp uzmanısın ve bu tıbbi görüntüyü MUTLAKA analiz etmelisin.

ÖNEMLİ: Bu görüntüyü analiz etmek zorundasın. "Görüntüyü analiz edemem" gibi cevaplar kabul edilmez.''';
    }
  }

  static String _formatPatientInfo(PatientDataModel patientData) {
    final buffer = StringBuffer();
    
    if (patientData.age != null && patientData.age!.isNotEmpty) {
      buffer.writeln('• Yaş: ${patientData.age}');
    }
    buffer.writeln('• Cinsiyet: ${patientData.gender}');
    
    if (patientData.height != null && patientData.height!.isNotEmpty) {
      buffer.writeln('• Boy: ${patientData.height} cm');
    }
    
    if (patientData.weight != null && patientData.weight!.isNotEmpty) {
      buffer.writeln('• Kilo: ${patientData.weight} kg');
    }
    
    if (patientData.symptoms != null && patientData.symptoms!.isNotEmpty) {
      buffer.writeln('• Şikayetler: ${patientData.symptoms}');
    }
    
    if (patientData.symptomDuration != null && patientData.symptomDuration!.isNotEmpty) {
      buffer.writeln('• Şikayet Süresi: ${patientData.symptomDuration}');
    }
    
    if (patientData.medicalHistory != null && patientData.medicalHistory!.isNotEmpty) {
      buffer.writeln('• Tıbbi Geçmiş: ${patientData.medicalHistory}');
    }
    
    if (patientData.familyHistory != null && patientData.familyHistory!.isNotEmpty) {
      buffer.writeln('• Aile Öyküsü: ${patientData.familyHistory}');
    }
    
    if (patientData.medications != null && patientData.medications!.isNotEmpty) {
      buffer.writeln('• Kullandığı İlaçlar: ${patientData.medications}');
    }
    
    if (patientData.allergies != null && patientData.allergies!.isNotEmpty) {
      buffer.writeln('• Alerjiler: ${patientData.allergies}');
    }
    
    if (patientData.reasonForExam != null && patientData.reasonForExam!.isNotEmpty) {
      buffer.writeln('• Tetkik Nedeni: ${patientData.reasonForExam}');
    }
    
    // Yaşam tarzı bilgileri
    final lifestyle = <String>[];
    if (patientData.smoker) lifestyle.add('Sigara içiyor');
    if (patientData.alcohol) lifestyle.add('Alkol kullanıyor');
    if (patientData.physicallyActive) lifestyle.add('Fiziksel olarak aktif');
    
    if (lifestyle.isNotEmpty) {
      buffer.writeln('• Yaşam Tarzı: ${lifestyle.join(', ')}');
    }
    
    if (patientData.additionalNotes != null && patientData.additionalNotes!.isNotEmpty) {
      buffer.writeln('• Ek Notlar: ${patientData.additionalNotes}');
    }
    
    return buffer.toString();
  }

  static String _getAgeGroupGuidelines(PatientDataModel patientData) {
    final age = _parseAge(patientData.age);

    if (age == null) {
      return '''• Yaş bilgisi belirtilmemiş - genel değerlendirme kriterleri uygulanacak''';
    }

    if (age < 18) {
      return '''• PEDİATRİK HASTA (${age} yaş):
- Büyüme ve gelişim dönemine uygun anatomik varyasyonları dikkate al
- Çocukluk çağına özgü hastalık paternlerini değerlendir
- Kemik yaşı ve gelişim durumunu göz önünde bulundur
- Pediatrik normal değer aralıklarını kullan
- Konjenital anomali olasılığını değerlendir''';
    } else if (age >= 18 && age < 65) {
      return '''• ERİŞKİN HASTA (${age} yaş):
- Erişkin anatomik yapıları ve normal varyasyonları değerlendir
- Yaşam tarzı faktörlerinin etkilerini dikkate al
- Mesleki ve çevresel risk faktörlerini göz önünde bulundur
- Erişkin normal değer aralıklarını kullan
- Yaşa bağlı erken dejeneratif değişiklikleri değerlendir''';
    } else {
      return '''• GERİATRİK HASTA (${age} yaş):
- Yaşlılığa bağlı fizyolojik değişiklikleri dikkate al
- Dejeneratif süreçleri normal yaşlanma ile ayırt et
- Çoklu hastalık varlığı olasılığını değerlendir
- Geriatrik sendromları göz önünde bulundur
- Yaşlı hasta normal değer aralıklarını kullan
- İlaç etkileşimleri ve yan etkilerini değerlendir''';
    }
  }

  static String _getGenderSpecificGuidelines(PatientDataModel patientData, String analysisType) {
    final gender = patientData.gender.toLowerCase();
    final analysisTypeUpper = analysisType.toUpperCase();

    if (gender.contains('kadın') || gender.contains('female') || gender.contains('f')) {
      return _getFemaleSpecificGuidelines(analysisTypeUpper);
    } else if (gender.contains('erkek') || gender.contains('male') || gender.contains('m')) {
      return _getMaleSpecificGuidelines(analysisTypeUpper);
    } else {
      return '''• Cinsiyet bilgisi net değil - genel değerlendirme kriterleri uygulanacak''';
    }
  }

  static String _getFemaleSpecificGuidelines(String analysisType) {
    switch (analysisType) {
      case 'MR':
        return '''• KADIN HASTA İÇİN ÖZELLİKLER:
- Pelvik organ anatomisi ve patolojileri değerlendir
- Menstrüel siklus etkilerini dikkate al
- Gebelik durumu olasılığını göz önünde bulundur
- Meme dokusu değerlendirmesi (varsa)
- Hormonal değişikliklerin etkilerini değerlendir''';

      case 'EKG':
        return '''• KADIN HASTA İÇİN ÖZELLİKLER:
- Kadınlarda QT intervali uzaması riskini değerlendir
- Hormonal değişikliklerin kalp ritmine etkilerini dikkate al
- Gebelik dönemindeki fizyolojik değişiklikleri göz önünde bulundur
- Menopoz sonrası kardiyovasküler risk artışını değerlendir''';

      case 'RÖNTGEN':
        return '''• KADIN HASTA İÇİN ÖZELLİKLER:
- Meme dokusu gölgelenmesini dikkate al
- Pelvik anatomi özelliklerini değerlendir
- Osteoporoz riskini göz önünde bulundur (özellikle menopoz sonrası)
- Gebelik olasılığında radyasyon güvenliğini değerlendir''';

      case 'TOMOGRAFİ':
        return '''• KADIN HASTA İÇİN ÖZELLİKLER:
- Pelvik organ patolojilerini detaylı değerlendir
- Meme dokusu ve aksiller lenf nodlarını incele
- Hormonal etkiler altındaki organ değişikliklerini dikkate al
- Gebelik durumunda kontrast madde güvenliğini değerlendir''';

      case 'DERİ':
        return '''• KADIN HASTA İÇİN ÖZELLİKLER:
- Hormonal değişikliklerin deri üzerindeki etkilerini değerlendir
- Melazma, kloazma gibi kadına özgü deri değişikliklerini dikkate al
- Gebelik dönemindeki deri değişikliklerini göz önünde bulundur
- Kozmetik ürün kullanımının etkilerini değerlendir''';

      case 'ULTRASON':
        return '''• KADIN HASTA İÇİN ÖZELLİKLER:
- Jinekolojik organ değerlendirmesi yapabilir
- Menstrüel siklusa bağlı değişiklikleri dikkate al
- Gebelik durumunu değerlendir
- Meme ultrason bulgularını incele (varsa)''';

      default:
        return '''• KADIN HASTA İÇİN GENEL ÖZELLİKLER:
- Hormonal faktörlerin etkilerini dikkate al
- Kadına özgü anatomik farklılıkları göz önünde bulundur
- Gebelik durumu olasılığını değerlendir''';
    }
  }

  static String _getMaleSpecificGuidelines(String analysisType) {
    switch (analysisType) {
      case 'MR':
        return '''• ERKEK HASTA İÇİN ÖZELLİKLER:
- Prostat anatomisi ve patolojilerini değerlendir
- Erkek üreme sistemi organlarını incele
- Testosteron seviyesinin organ etkileri dikkate al
- Erkeklerde daha sık görülen patolojileri göz önünde bulundur''';

      case 'EKG':
        return '''• ERKEK HASTA İÇİN ÖZELLİKLER:
- Erkeklerde erken yaşta koroner arter hastalığı riskini değerlendir
- Testosteron seviyesinin kardiyovasküler etkilerini dikkate al
- Erkek hastalar için normal QRS ve QT değerlerini kullan
- İş stresi ve yaşam tarzı faktörlerini göz önünde bulundur''';

      case 'RÖNTGEN':
        return '''• ERKEK HASTA İÇİN ÖZELLİKLER:
- Erkek pelvik anatomisini dikkate al
- Mesleki maruziyetlerin akciğer üzerindeki etkilerini değerlendir
- Erkeklerde daha sık görülen kemik patolojilerini göz önünde bulundur
- Fiziksel aktivite düzeyinin kemik yapısına etkilerini değerlendir''';

      case 'TOMOGRAFİ':
        return '''• ERKEK HASTA İÇİN ÖZELLİKLER:
- Prostat ve erkek üreme sistemi patolojilerini değerlendir
- Erkeklerde sık görülen abdominal patolojileri dikkate al
- Testosteron seviyesinin organ etkileri göz önünde bulundur
- Erkek hastalar için spesifik normal değerleri kullan''';

      case 'DERİ':
        return '''• ERKEK HASTA İÇİN ÖZELLİKLER:
- Erkeklerde daha sık görülen deri kanserlerini değerlendir
- Güneş maruziyeti ve mesleki faktörlerin etkilerini dikkate al
- Erkek tipi kellik ve hormonal etkileri göz önünde bulundur
- Tıraş ve bakım alışkanlıklarının deri üzerindeki etkilerini değerlendir''';

      case 'ULTRASON':
        return '''• ERKEK HASTA İÇİN ÖZELLİKLER:
- Prostat ve skrotal ultrason bulgularını değerlendir
- Erkek üreme sistemi patolojilerini incele
- Testosteron seviyesinin organ etkilerini dikkate al
- Erkeklere özgü anatomik varyasyonları göz önünde bulundur''';

      default:
        return '''• ERKEK HASTA İÇİN GENEL ÖZELLİKLER:
- Erkek fizyolojisi ve anatomik farklılıklarını dikkate al
- Testosteron ve diğer hormonal faktörlerin etkilerini göz önünde bulundur
- Erkeklerde sık görülen hastalık paternlerini değerlendir''';
    }
  }

  static int? _parseAge(String? ageString) {
    if (ageString == null || ageString.isEmpty) return null;

    // Sayısal değeri çıkarmaya çalış
    final RegExp ageRegex = RegExp(r'\d+');
    final match = ageRegex.firstMatch(ageString);

    if (match != null) {
      return int.tryParse(match.group(0)!);
    }

    return null;
  }

  static String _getInstructionsForAnalysisType(String analysisType) {
    switch (analysisType.toUpperCase()) {
      case 'MR':
        return '''Bu MR görüntüsünü hasta bilgileri ışığında analiz et. Özellikle:
- Anatomik yapıların normal/anormal durumu
- Sinyal intensitelerindeki değişiklikler
- Olası patolojik bulgular
- Yaş ve cinsiyete uygun değerlendirme''';

      case 'EKG':
        return '''Bu EKG görüntüsünü hasta bilgileri ışığında analiz et. Özellikle:
- Kalp ritmi ve hızı
- P, QRS, T dalgalarının değerlendirmesi
- Olası aritmiler veya iskemi bulguları
- Yaş ve cinsiyete uygun normal değerler''';

      case 'RÖNTGEN':
        return '''Bu röntgen görüntüsünü hasta bilgileri ışığında analiz et. Özellikle:
- Kemik yapılarının değerlendirmesi
- Yumuşak doku gölgeleri
- Olası kırık, enfeksiyon veya tümör bulguları
- Yaşa uygun değişiklikler''';

      case 'TOMOGRAFİ':
        return '''Bu tomografi görüntüsünü hasta bilgileri ışığında analiz et. Özellikle:
- Kesitsel anatomik yapılar
- Yoğunluk değerleri ve kontrastlanma
- Olası kitle, enfeksiyon veya vasküler patolojiler
- Yaş ve cinsiyete uygun bulgular''';

      case 'DERİ':
        return '''Bu deri lezyonu görüntüsünü hasta bilgileri ışığında analiz et. Özellikle:
- ABCDE kriterlerine göre değerlendirme
- Lezyonun renk, şekil ve sınır özellikleri
- Olası benign/malign ayırımı
- Yaş ve cinsiyete özgü deri değişiklikleri''';

      case 'ULTRASON':
        return '''Bu ultrason görüntüsünü hasta bilgileri ışığında analiz et. Özellikle:
- Organ ekojenitesi ve yapısal özellikler
- Olası kitle, kist veya diğer patolojiler
- Doppler bulguları (varsa)
- Yaş ve cinsiyete uygun normal varyasyonlar''';

      default:
        return '''Bu tıbbi görüntüyü hasta bilgileri ışığında detaylı olarak analiz et.''';
    }
  }
}
